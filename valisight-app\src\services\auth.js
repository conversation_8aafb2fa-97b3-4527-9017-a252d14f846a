import axiosInstance from "./axiosInstance";

export const login = async (data) => {
  return await axiosInstance.post("/auth/login", data);
};

export const getCurrentUser = async () => {
  return await axiosInstance.get("/auth/me");
};

export const forgotPassword = async (email) => {
  return await axiosInstance.post("/auth/forget-password", { email });
};

export const resetPassword = async (data, token) => {
  return await axiosInstance.post(`/auth/reset-password/${token}`, data);
};

export const resetPasswordLink = async (email) => {
  return await axiosInstance.post("/auth/reset-password-email", { email });
};

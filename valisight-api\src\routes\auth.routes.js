import { Router } from 'express';
export const authRoute = Router();
import * as authController from '../controllers/auth.controller.js';
import { authenticate } from '../middleware/auth.middleware.js';

authRoute.post('/register', authController.register);

authRoute.post('/login', authController.login);

authRoute.get('/me', authenticate, authController.getCurrentUser);

authRoute.post('/forget-password', authController.forgetPassword);
authRoute.post('/reset-password-email', authController.resetPasswordEmail);
authRoute.post('/reset-password/:token', authController.resetPassword);
